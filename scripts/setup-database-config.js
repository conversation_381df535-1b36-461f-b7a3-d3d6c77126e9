#!/usr/bin/env node

/**
 * Secure Database Configuration Setup
 * 
 * This script securely configures database settings for the notification system
 * without exposing sensitive credentials in version-controlled files.
 * 
 * Usage:
 *   node scripts/setup-database-config.js
 * 
 * Environment variables required:
 *   - SUPABASE_SERVICE_ROLE_KEY: Service role key from Supabase dashboard
 *   - NEXT_PUBLIC_SUPABASE_URL: Supabase project URL
 *   - DIRECT_URL: Direct database connection URL
 */

const { execSync } = require('child_process');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });

function validateEnvironmentVariables() {
  const required = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_SUPABASE_URL',
    'DIRECT_URL'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`   - ${key}`));
    console.error('\nPlease check your .env.local file and ensure all required variables are set.');
    process.exit(1);
  }

  console.log('✅ All required environment variables are present');
}

function setupDatabaseSettings() {
  console.log('🔧 Configuring secure database settings...');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  // Create SQL commands to set database configuration
  const sqlCommands = [
    `ALTER DATABASE postgres SET app.supabase_url = '${supabaseUrl}';`,
    `ALTER DATABASE postgres SET app.supabase_service_role_key = '${serviceRoleKey}';`
  ];

  try {
    // Execute each SQL command
    sqlCommands.forEach((sql, index) => {
      console.log(`   Setting configuration ${index + 1}/2...`);
      
      const command = `psql "${process.env.DIRECT_URL}" -c "${sql}"`;
      execSync(command, { stdio: 'pipe' });
    });

    console.log('✅ Database settings configured successfully');
    console.log('🔒 Service role key is now securely stored in database configuration');
    
  } catch (error) {
    console.error('❌ Failed to configure database settings:');
    console.error(error.message);
    
    console.log('\n💡 Alternative setup method:');
    console.log('If the automatic setup fails, you can manually run these SQL commands:');
    console.log('');
    sqlCommands.forEach((sql, index) => {
      console.log(`${index + 1}. ${sql}`);
    });
    
    process.exit(1);
  }
}

function testConfiguration() {
  console.log('🧪 Testing database configuration...');

  try {
    const testSql = `SELECT current_setting('app.supabase_url', true) as url, 
                            CASE 
                              WHEN current_setting('app.supabase_service_role_key', true) IS NOT NULL 
                              THEN 'CONFIGURED' 
                              ELSE 'NOT_CONFIGURED' 
                            END as key_status;`;
    
    const command = `psql "${process.env.DIRECT_URL}" -c "${testSql}"`;
    const result = execSync(command, { encoding: 'utf8' });
    
    console.log('📊 Configuration test results:');
    console.log(result);
    
    if (result.includes('CONFIGURED')) {
      console.log('✅ Database configuration test passed');
    } else {
      console.log('❌ Database configuration test failed');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Configuration test failed:', error.message);
    process.exit(1);
  }
}

function main() {
  console.log('🚀 Starting secure database configuration setup...\n');

  try {
    validateEnvironmentVariables();
    setupDatabaseSettings();
    testConfiguration();
    
    console.log('\n🎉 Setup completed successfully!');
    console.log('🔐 The notification system is now configured with secure credentials');
    console.log('📝 No sensitive keys are stored in version-controlled files');
    
  } catch (error) {
    console.error('\n💥 Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { validateEnvironmentVariables, setupDatabaseSettings, testConfiguration };
